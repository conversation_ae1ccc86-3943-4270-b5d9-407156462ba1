# Core Problems

## 1. Discovery Mechanism

- **Goal:** provide new node with valid node record from within gossip swarm without having to parse through to many records
- **Core Question:** "Where do I look?"
- **Solution-1:** mainline mutable records from ed25519 derived `keypairs` and `salt`. Multiple record versions can be recovered from a single `get_all(pubkey, salt)` call (a vec of `MutableItem` is returned). Key derivation is coupled to the topic name and the current unix minute = `floor(unixtime/60)` to prevent replay attacks and stale record graveyarding.
  
## 2. Record Identity
- **Goal:** prove that a record was written by the node it claims to be from
- **Core Question:** "How do I verify the author of a record?"
- **Solution-1:** ed25519 signature over record content tied to current unix miunte to prevent replay attacks.

## 3. Security / Proof of Mainnet Connectivity
- **Goal:** prove that the node in the record has established and maintained live, interactive connections with a random, statistically significant sample of the honest-majority network
- **Core Question:** "Is this node a citizen of the real network, or just an island or a sybil?"
- **Solution-1:** proof of relay, in a nutshell you show the output of a procedure involvingthe messages you have received so that joining nodes can compare to what other nodes have as their message flow without copying someone else's message flow, then go from there.
- **Solution-2:** proof of liveness, you have the 5 active peers (every node tries to have 5 acrive peers and 30 passive 30 passive ones in the iroh-gossip's [HyParView](https://asc.di.fct.unl.pt/~jleitao/pdf/dsn07-leitao.pdf) default implementation) sign a unix_minute derived hash for example: `sig( hash( topic + unix_minute + node_id ) )`. this is **not** save from sybil attacks and i am not a big fan of it right now.
- **Solution-3:** iroh-gossip is inherenetly unsafe and designed to only give out a node id to a trust worthy set of peers. This is an important consideration since any "topic tracker" that exposes node ids into a gossip network that has no build in mechanism to keep out bad actors and protocol abusers. this makes the publishing of node ids and the topics they belong to very dangerous. Considering this, a topic needs a "secret" that can only be derived based on information the joining nodes already have (the individual app implementations can decide on their own). So we get a secret and we use it to generate another keypair, this time for encryption. we can stick to curve 25519 or some other encryption compatible key schema. this is to make sure we never publicly expose the node ids alongside the topic. I build a "gossip with permissions" on top of iroh-gossip before and I created a crate for translating ed25519 keys to X25519 keys compatible with hpke encryption: [https://github.com/rustonbsd/ed25519-dalek-hpke](https://github.com/rustonbsd/ed25519-dalek-hpk) (this should be safe enough for our purposes). tldr: we create one time encryption keys and encrypt the record with it and then use the shared secret to encrypt the one time encryption key and append it to the record. Nodes with the secret can then decrypt the key and subsequently the record. If we rely on this shared secret we are vulnerable to key compromize, but since iroh-gossip leaves security to the app developer, I think we can do the same thing. Implementations can have access to secret rotation and decide what secret seed is used when or so. (This route is my vote)

## 4. System Stability Mechanism
- **Goal:** prevent the discovery mechanism from being overloaded by too many writes (DDoS/spam) or synchronized access patterns (thundering herd)
- **Core Question:** "What gives a node the right to write, and what stops everyone from writing at the same time?"
- **Example solutions:** random time delays (since we can trust everyone), Proof-of-Work, Verifiable Delay Functions (VDFs), or probabilistic slotting schemes that ensure writes are rate-limited and desynchronized
- **Solution-1:** a combination of random time delays (since we can trust everyone) and a simple multi slotting scheme. Lets say N slots and you calculate the solt (we use the different salts in mutable record writes as different slots) `salt = hash ("slot:" + topic + unix_minute + (hash( topic + unix_minute + my_node_id ) % N) )`

---

# My Proposal - P01
the following is my proposal based on the solutions i voted for above.
this doesn't mean that the solutions are right nor that there aren't better ones i haven't considered.
but I think we could use everything above this line to formalize the requirements some more.

## Publishing procedure (refined implementation)

The publishing procedure is a rate-limited mechanism that prevents DHT overload while ensuring active participation in the gossip network. `publish_proc` function in `p01_refined.rs`:

### Constants
- `MAX_BOOTSTRAP_RECORDS`: 10 (maximum active participant records allowed per unix minute)
- DHT timeout: 10 seconds for get_mutable operations
- Retry count: 3 attempts for DHT publishing operations
- Random delay: 0-2000ms between retry attempts

### Publishing Procedure

1. **Record Discovery**
   - Call `get_unix_minute_records()` to fetch, decrypt, and verify existing records for the current unix minute
   - Use the same key derivation as bootstrap:
     - Derive signing keypair: `keypair_seed = hash(topic + unix_minute)`
     - Derive encryption keypair: `enc_keypair_seed = secret_rotation_function.get_unix_minute_secret(topic, unix_minute, initial_secret_hash)`
     - Calculate salt: `salt = hash(topic + unix_minute)`
     - Query DHT: `get_mutable(signing_pubkey, salt)` with 10s timeout

2. **Active Participant Filtering**
   - Filter records to include only "active participants" - records that have:
     - Non-zero entries in `active_peers[5]` array, OR
     - Non-zero entries in `last_message_hashes[5]` array
   - This ensures only nodes actively participating in gossip are counted and network *Bubbles* are detected based on the `last_message_hashes` and merged.

3. **Rate Limiting Check**
   - If `active_participant_records.len() >= MAX_BOOTSTRAP_RECORDS` (10):
     - **Do not publish** - return existing records to prevent DHT overload
     - This implements the core rate limiting mechanism

4. **Record Creation** (if under limit)
   - Prepare `active_peers[5]` array:
     - Fill with up to 5 current iroh-gossip neighbors
     - Remaining slots filled with zeros `[0; 32]`
   - Prepare `last_message_hashes[5]` array:
     - Fill with up to 5 recent message hashes for proof of relay
     - Remaining slots filled with zeros `[0; 32]`

5. **Record Signing and Publishing**
   - Create signed record using `Record::sign()`:
     - Include: `topic_hash`, `unix_minute`, `node_id`, `active_peers`, `last_message_hashes`
     - Sign with node's ed25519 signing key (`iroh::NodeId`)
   - Encrypt record using one-time encryption key
   - Publish to DHT via `publish_unix_minute_record()` with 3 retry attempts

6. **Error Handling**
   - DHT timeouts return empty record sets (non-fatal)
   - Failed record decryption/verification are ignored
   - Random delays between retries prevent synchronized access patterns

### Publishing Flow Diagram

```mermaid
flowchart TD
  A[Start Publishing Procedure] --> B[Get Current Unix Minute]
  B --> C[Derive Keys: Signing & Encryption]
  C --> D[Calculate Salt: hash = topic + unix_minute]
  D --> E[Query DHT: get_mutable = signing_pubkey, salt; Timeout: 10s]

  E --> F[Decrypt & Verify Records]
  F --> G[Filter Active Participants]
  G --> H{Active Records >= MAX_BOOTSTRAP_RECORDS = 10?}

  H -- Yes --> I[Return Existing Records - Rate Limited]
  H -- No --> J[Prepare Active Peers Array]

  J --> K[Fill active_peers with up to 5 gossip neighbors]
  K --> L[Prepare Last Message Hashes Array]
  L --> M[Fill last_message_hashes with up to 5 recent hashes]

  M --> N[Create Signed Record]
  N --> O[Sign with: topic + unix_minute + node_id + active_peers + last_message_hashes]
  O --> P[Encrypt Record with One-Time Key]
  P --> Q[Publish to DHT with 3 Retries]

  Q --> R{Publish Success?}
  R -- Yes --> S[Return All Records Including Own]
  R -- No --> T[Random Delay 0-2000ms]
  T --> U{Retries Left?}
  U -- Yes --> Q
  U -- No --> V[Return Error - Failed to Publish]

  style A fill:#f4f4f4,stroke:#333,stroke-width:1px
  style I fill:#ffcccc,stroke:#333,stroke-width:1px
  style S fill:#ccffcc,stroke:#333,stroke-width:1px
  style V fill:#ffcccc,stroke:#333,stroke-width:1px
  style G fill:#f4f4f4,stroke:#333,stroke-width:1px
  style N fill:#f4f4f4,stroke:#333,stroke-width:1px
```

## Bootstrap procedure (refined implementation)

The bootstrap procedure is a continuous loop that attempts to discover and connect to existing nodes in the gossip network. Here's the detailed flow based on the `p01_refined.rs` implementation:

### Constants
- `MAX_JOIN_PEERS_COUNT`: 100 (maximum peers to attempt joining)
- `MAX_BOOTSTRAP_RECORDS`: 10 (maximum records allowed per unix minute)
- DHT timeout: 10 seconds for get_mutable operations
- Connection retry delay: 100ms between attempts
- Final connection check delay: 500ms

### Bootstrap Loop

1. **Initial Setup**
   - Subscribe to the gossip topic using `topic_id.hash`
   - Initialize `last_published_unix_minute = 0` to track publishing state
   - Enter the main bootstrap loop

2. **Connection Check**
   - Check if already connected to at least one gossip peer via `gossip_receiver.is_joined()`
   - If connected, return successfully with gossip sender/receiver pair

3. **Time Window Selection**
   - On first attempt: check previous unix minute (`unix_minute(-1)`)
   - On subsequent attempts: check current unix minute (`unix_minute(0)`)
   - This ensures we reliably discover existing gossip network records on the first try

4. **Record Discovery**
   - Call `get_unix_minute_records()` to fetch, decrypt, and verify records:
     - Derive signing keypair: `keypair_seed = hash(topic + unix_minute)`
     - Derive encryption keypair: `enc_keypair_seed = secret_rotation_function.get_unix_minute_secret(topic, unix_minute, initial_secret_hash)`
     - Calculate salt: `salt = hash(topic + unix_minute)`
     - Query DHT: `get_mutable(signing_pubkey, salt)` with 10s timeout
     - Decrypt each record using the encryption keypair
     - Verify signature, unix_minute, and topic hash
     - Filter out own records (matching node_id)

5. **If no valid Records Found**
   - If no valid records found, attempt to publish own record via `publish_proc()`
   - Only publish if haven't published in this unix minute
   - Sleep 100ms and continue loop

6. **else if valid Records Found**
   - Extract bootstrap nodes from records:
     - Include `record.node_id` (the publisher)
     - Include all non-zero entries from `record.active_peers[5]`
   - Convert byte arrays to valid `iroh::NodeId` instances

7. **Connection Attempts**
   - Check again if already connected (someone might have connected to us)
   - If not connected, attempt to join peers one by one:
     - Call `gossip_sender.join_peers(vec![node_id])` for each bootstrap node
     - Sleep 100ms between attempts to minimize disruption
     - Break early if connection established
     - (findings showed connecting more too many nodes at once can cause the formation of netowrk *Bubbles*, isolated subnetworks that are not connected to the main network)

8. **Final Connection Verification**
   - If still not connected, wait 500ms for iroh-gossip connection timeout
   - Check `gossip_receiver.is_joined()` one final time
   - If connected: return successfully; spawn publisher task
   - If not connected: attempt to publish own record (if not done this minute)
   - Sleep 100ms and continue loop

### Error Handling
- DHT timeouts return empty record sets (non-fatal)
- Failed record decryption/verification are treated as invalid records and ignored
- Failed peer connections don't interrupt the process
- Publishing failures don't prevent continued bootstrapping

### Bootstrap Flow Diagram

```mermaid
flowchart TD
  A[Start Bootstrap] --> B[Subscribe to Gossip Topic]
  B --> C[Initialize last_published_unix_minute = 0]
  C --> D{Already Connected?}

  D -- Yes --> Z[Return Success]
  D -- No --> E[Determine Unix Minute]

  E --> F{First attempt?}
  F -- Yes --> G[Use Previous Minute unix_minute = -1]
  F -- No  --> H[Use Current Minute unix_minute = 0 ]

  G --> I[Get Unix Minute Records]
  H --> I

  I --> J[Derive Signing Keypair hash = topic + unix_minute ]
  J --> K[Derive Encryption Keypair from shared secret]
  K --> L[Calculate Salt hash = topic + unix_minute]
  L --> M[Query DHT: get_mutable = signing_pubkey, salt; Timeout: 10s]

  M --> N{Records Found?}
  N -- No --> O{Published This Minute?}
  O -- No  --> P[Publish Own Record via publish_proc]
  O -- Yes --> Q[Sleep 100ms]
  P --> Q
  Q --> D

  N -- Yes --> R[Decrypt & Verify Records]
  R --> S[Filter Valid Records 1.Decrypt with encryption key 2.Verify signature 3.Check topic & unix_minute 4.Exclude own node_id]
  S --> T[Extract Bootstrap Nodes 1.record.node_id 2.record.active_peers]
  T --> U[Convert to iroh::NodeId]
  U --> V{Already Connected?}
  V -- Yes --> Z
  V -- No  --> W[Join Peers One by One]

  W --> X[For each bootstrap node: gossip_sender.join_peers = node_id]
  X --> Y[Sleep 100ms]
  Y --> AA{Connected?}
  AA -- Yes --> Z
  AA -- No  --> BB{More Nodes?}
  BB -- Yes --> X
  BB -- No  --> CC[Sleep 500ms Final connection timeout]

  CC --> DD{Connected?}
  DD -- Yes --> Z
  DD -- No  --> EE{Should Publish?}
  EE -- Yes --> FF[Publish Own Record]
  EE -- No  --> GG[Sleep 100ms]
  FF --> GG
  GG --> D

  style A fill:#f4f4f4,stroke:#333,stroke-width:1px
  style Z fill:#f4f4f4,stroke:#333,stroke-width:1px
  style I fill:#f4f4f4,stroke:#333,stroke-width:1px
  style R fill:#f4f4f4,stroke:#333,stroke-width:1px
  style W fill:#f4f4f4,stroke:#333,stroke-width:1px
```


# P


## Record structure (refined implementation)

```rust
// 489 bytes total (S=5)
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct Record {
    // Record Content
    topic: [u8; 32],                    // sha512(topic_string)[..32]
    unix_minute: u64,                   // floor(unixtime / 60)
    node_id: [u8; 32],                  // publisher ed25519 public key
    active_peers: [[u8; 32]; 5],        // 5 node ids of active gossip peers
    last_message_hashes: [[u8; 32]; 5], // 5 recent message hashes for proof of relay

    // Record Signature
    signature: [u8; 64],                // ed25519 signature over above fields
                                        // signed by the publisher's ed25519 private key
}

// Variable size (>= 493 bytes at S=5)
#[derive(Debug, Clone)]
pub struct EncryptedRecord {
    encrypted_record: Vec<u8>,          // encrypted Record using one-time key
    encrypted_decryption_key: Vec<u8>,  // one-time decryption key encrypted with
                                        // shared secret derived encryption key
}
```

### Key Changes from Original Design
1. **Removed `slot` field**: Slot information is now encoded in the DHT salt parameter
2. **Added `last_message_hashes`**: Provides proof of relay/liveness (5 recent message hashes)
3. **Variable-size encryption**: `EncryptedRecord` uses `Vec<u8>` for flexibility
4. **Serialization format**:
   - `EncryptedRecord`: `[encrypted_record_len: u32][encrypted_record][encrypted_decryption_key]`
   - `Record`: Direct concatenation of all fields in order

## Verification (refined implementation)

The `Record::verify()` method performs the following checks:

1. **Topic Verification**: Verify `record.topic` matches the expected topic hash
2. **Time Verification**: Verify `record.unix_minute` matches the unix minute used for key derivation
3. **Signature Verification**:
   - Extract signature data: all record bytes except the last 64 bytes (signature)
   - Signature data includes: `topic + unix_minute + node_id + active_peers + last_message_hashes`
   - Verify ed25519 signature using `record.node_id` as the public key
   - Use `verify_strict()` for enhanced security

### Additional Filtering
- **Self-exclusion**: Records matching the verifying node's `node_id` are filtered out
- **Decryption validation**: Records that fail decryption with the shared secret are rejected
- **Encoding validation**: Records that fail to decode from bytes are rejected

## Encryption, Decryption
I used a one time key encryption scheme in the advanced-gossip project, we can use (for now) the same here. 
The above mentioned [https://github.com/rustonbsd/ed25519-dalek-hpke](https://github.com/rustonbsd/ed25519-dalek-hpk) crate was written for exactly this purpose.

The reference below uses one time keys to sign records for all peers that are allowed to read it. 
That part can be ignored, it is about the onetime keys and the interplay between ed25519 and hpke.

here the one time enc code as a reference: https://github.com/rustonbsd/advanced-gossip/blob/main/src/structs.rs#L239-L264